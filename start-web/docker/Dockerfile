FROM --platform=linux/amd64 registry-enterprise-registry.cn-hangzhou.cr.aliyuncs.com/tech-test/openjdk:8u351
COPY target/start-web-1.0.0-SNAPSHOT.jar /home/<USER>/start-web.jar

EXPOSE 8080
## 环境变量
ENV APP_PROFILE=dev

RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && echo 'Asia/Shanghai' >/etc/timezone

#ENV JAVA_OPTS="-javaagent:/skywalking/agent/skywalking-agent.jar"
## jvm 参数自行修改
ENV JAVA_OPTS="-server -Dfile.encoding=UTF-8 -Dspring.jmx.enabled=false -Dserver.port=8080 -Dspring.profiles.active=${APP_PROFILE} -Xms1g -Xmx2g -Xss256k -XX:MetaspaceSize=128M -XX:NewRatio=2 -XX:SurvivorRatio=4 -XX:+UseConcMarkSweepGC -XX:ConcGCThreads=4 -XX:ParallelGCThreads=4 -XX:+CMSScavengeBeforeRemark -XX:PretenureSizeThreshold=16m -XX:+UseCMSInitiatingOccupancyOnly -XX:CMSInitiatingOccupancyFraction=70 -XX:CMSMaxAbortablePrecleanTime=3000 -XX:+CMSParallelRemarkEnabled -XX:+ParallelRefProcEnabled -XX:-OmitStackTraceInFastThrow"
## 启动参数
CMD [ "sh", "-c", "java ${JAVA_OPTS} -Djava.security.egd=file:/dev/./urandom -jar /home/<USER>/start-web.jar" ]