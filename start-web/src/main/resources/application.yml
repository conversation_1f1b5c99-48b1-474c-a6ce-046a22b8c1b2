server:
  port: 7080
spring:
  application:
    name: start
  profiles:
    active: ${spring.profile:dev}
  servlet:
    multipart:
      max-file-size: 256MB
      max-request-size: 256MB
  main:
    allow-bean-definition-overriding: true
  jackson:
    default-property-inclusion: non_null
    serialization:
      WRITE_DATES_AS_TIMESTAMPS: true
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      auto-commit: true
      minimum-idle: 5
      maximum-pool-size: 20
      connection-timeout: 20000
      idle-timeout: 300000
      max-lifetime: 900000
      connection-test-query: select 1 from dual
      pool-name: HikariPool
      data-source-properties:
        cachePrepStmts: true
        prepStmtCacheSize: 250
        prepStmtCacheSqlLimit: 2048
        useServerPrepStmts: true
        useLocalSessionState: true
        rewriteBatchedStatements: true
        cacheResultSetMetadata: true
        cacheServerConfiguration: true
        elideSetAutoCommits: true
        maintainTimeStats: false
  redis:
    timeout: 3000
    jedis:
      pool:
        max-active: 16
        max-idle: 16
        min-idle: 0
        max-wait: -1ms

logging:
  level:
    root: info
    org.springframework.boot: info
    com.alibaba.nacos.client.naming: warn
    com.ryytn.start.dao.mapper: debug

jasypt:
  encryptor:
    password: Ryytn!123456

forest:
  # 连接池最大连接数
  max-connections: 500
  # 连接超时时间，单位毫秒
  connect-timeout: 3000
  # 数据读取超时时间，单位毫秒
  read-timeout: 30000
  base-address-scheme: http
  max-retry-count: 0
  converters:
    json:
      type: com.dtflys.forest.converter.json.ForestJacksonConverter
  variables:
    # 这个可以换成shenyu的地址，接口中实际地址带上前缀，再配合MSE或Nginx使用
    start-web-url: http://127.0.0.1:8080

jimmer:
  dialect: org.babyfish.jimmer.sql.dialect.MySqlDialect
  offset-optimizing-threshold: 10
  id-only-target-checking-level: ALL
  database-validation-mode: ERROR
  in-list-padding-enabled: true
  default-dissociation-action-checkable: false
  is-foreign-key-enabled-by-default: false
  show-sql: true
  pretty-sql: true
#  client:
#    ts:
#      path: /ts.zip
#    openapi:
#      path: /openapi.yml
#      ui-path: /openapi.html
#      properties:
#        info:
#          title: Jimmer REST Example(Java)
#          description: This is the OpenAPI UI of Jimmer REST Example(Java)
#          version: 0.8.62

#dubbo:
#  application:
#    name: ${spring.application.name}
#    qos-enable: false
#  protocol:
#    threadpool: fixed
#    threads: 200
#    # netty、netty3
#    transporter: netty
#    # HeaderExchanger
#    exchanger: header
#    # all、direct、message、connection、header
#    dispatcher: message
#  scan:
#    base-packages: com.ryytn.start.service.entrance
#  registry:
#    address: nacos://${nacos.addr:**************:8848}
#    group: ${spring.application.name}
#    username: ${nacos.username:nacos}
#    password: ${nacos.password:nacos}
#  consumer:
#    check: false
#    lazy: true

#easy-query:
#  enable: true
#  database: mysql
#  #对象属性和数据库列名的转换器
#  name-conversion: underlined
#  #当执行物理删除是否报错,true表示报错,false表示不报错,默认true,如果配置为true,可以通过allowDeleteStament来实现允许
#  delete-throw: false
#  #是否打印sql 默认true 需要配置log信息才可以 默认实现sl4jimpl
#  print-sql: true
#  #配置为默认追踪,但是如果不添加@EasyQueryTrack注解还是不会启用所以建议开启这个如果需要只需要额外添加注解即可
#  default-track: true
#  #sqlNativeSegment输入和格式化无需处理单引号会自动处理为双单引号
#  keep-native-style: true