spring:
  cloud:
    nacos:
      config:
        file-extension: yaml
        # https://nacos-test.ryytngroup.com/
        server-addr: ${nacos.addr:172.16.100.249:8848}
        username: ${nacos.username:nacos}
        password: ${nacos.password:nacos}
        # 使用自定义空间时需要使用MD5的ID
        namespace: ${nacos.namespace:61647f4d-8da4-4258-b51a-21c935b3ab6d}
        group: start
        max-retry: 3
        config-long-poll-timeout: 10000
        extension-configs:
          - data-id: dynamic.yaml
            group: start
            refresh: true
          - data-id: common.yaml
            group: common
            refresh: true