package com.ryytn.start.web;

import cn.hutool.extra.spring.EnableSpringUtil;
import org.babyfish.jimmer.spring.repository.EnableJimmerRepositories;
import org.babyfish.jimmer.sql.EnableDtoGeneration;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;

/**
 * <AUTHOR>
 * @since 2020-10-13
 */
@EnableDtoGeneration
@SpringBootApplication(scanBasePackages = "com.ryytn.start")
@EnableConfigurationProperties
@EnableSpringUtil
@EnableJimmerRepositories(basePackages = {"com.ryytn.start.manager.repository"})
public class WebApplication {

  public static void main(String[] args) {
    SpringApplication.run(WebApplication.class, args);
  }
}
