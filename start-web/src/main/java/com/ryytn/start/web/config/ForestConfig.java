package com.ryytn.start.web.config;

import com.dtflys.forest.converter.json.ForestJacksonConverter;
import com.dtflys.forest.converter.json.ForestJsonConverter;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * ForestConfig
 *
 * <AUTHOR>
 * @date 10/12/24
 */
@Configuration
public class ForestConfig {

    @Bean
    public ForestJsonConverter forestJacksonConverter(ObjectMapper objectMapper) {
        // 注入 SpringBoot 上下文中的 ObjectMapper 对象
        return new ForestJacksonConverter(objectMapper);
    }

}
