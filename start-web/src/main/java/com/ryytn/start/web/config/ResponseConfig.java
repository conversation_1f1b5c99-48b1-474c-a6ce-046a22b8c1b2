package com.ryytn.start.web.config;

import cn.hutool.extra.spring.SpringUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ryytn.start.common.Result;
import lombok.SneakyThrows;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

@ControllerAdvice({"com.ryytn.start"})
public class ResponseConfig implements ResponseBodyAdvice<Object> {

    private static final MediaType APPLICATION_PLUS_JSON = new MediaType("application", "*+json");

    @Override
    public boolean supports(MethodParameter returnType, Class<? extends HttpMessageConverter<?>> converterType) {
        // 只对返回类型不是Result的进行封装
        return !returnType.getParameterType().equals(Result.class);
    }

    @SneakyThrows
    @Override
    public Object beforeBodyWrite(Object body, MethodParameter returnType, MediaType type,
        Class<? extends HttpMessageConverter<?>> selectedConverterType,
        ServerHttpRequest request, ServerHttpResponse response) {
        if (type.isConcrete()
            && (type.isCompatibleWith(MediaType.APPLICATION_JSON) ||
                type.isCompatibleWith(APPLICATION_PLUS_JSON))) {
            // 如果返回的是null，则封装成Result.buildSuccess()
            if (body == null) {
                return Result.buildSuccess();
            }

            // 如果返回的是Result，则直接返回
            if (body instanceof Result) {
                return body;
            }

            // 其他情况封装成Result.buildSuccess(body)
            Result result = Result.buildSuccess(body);
            // JSON类型的字符串结果需要特殊处理，否则StringHttpMessageConverter会执行失败
            if (body instanceof String
                && selectedConverterType == StringHttpMessageConverter.class) {
                ObjectMapper objectMapper = SpringUtil.getBean(ObjectMapper.class);
                return objectMapper.writeValueAsString(result);
            }

            return result;
        } else {
            return body;
        }
    }
}