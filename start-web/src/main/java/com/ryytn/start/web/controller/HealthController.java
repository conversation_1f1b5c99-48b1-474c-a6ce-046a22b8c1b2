package com.ryytn.start.web.controller;

import lombok.extern.slf4j.Slf4j;
import org.babyfish.jimmer.client.ApiIgnore;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @since 2020-10-13
 */
@ApiIgnore
@RestController
@Slf4j
public class HealthController {

  @ApiIgnore
  @GetMapping("/status")
  public String doHC(HttpServletRequest request, HttpServletResponse response) {
    return "success";
  }
}
