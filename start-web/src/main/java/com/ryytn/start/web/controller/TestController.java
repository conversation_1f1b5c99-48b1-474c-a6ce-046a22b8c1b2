package com.ryytn.start.web.controller;

import com.alibaba.fastjson2.JSON;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.ryytn.start.api.dto.UserInputDto;
import com.ryytn.start.api.entity.CompanyTable;
import com.ryytn.start.api.entity.Fetchers;
import com.ryytn.start.api.entity.Tables;
import com.ryytn.start.api.entity.User;
import com.ryytn.start.api.entity.UserTable;
import com.ryytn.start.api.service.http.HttpUserServiceApi;
import com.ryytn.start.common.Result;
import com.ryytn.start.manager.config.DynamicConfig;
import com.ryytn.start.manager.repository.CompanyRepository;
import com.ryytn.start.manager.repository.UserRepository;
import lombok.extern.slf4j.Slf4j;
import org.babyfish.jimmer.client.FetchBy;
import org.babyfish.jimmer.sql.fetcher.Fetcher;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

import static com.ryytn.start.api.entity.Fetchers.USER_FETCHER;

/**
 * <AUTHOR>
 * @since 2020-10-14
 */
@RestController
@Slf4j
@Validated
public class TestController {

    @Value("${common}")
    private String test;

    @Resource
    private Environment environment;
    @Resource
    private DynamicConfig dynamicConfig;
    @Resource
    private HttpUserServiceApi userServiceApi;

    @Autowired
    private UserRepository userRepository;
    @Autowired
    private CompanyRepository companyRepository;

    //----------------------------------------------------------------------------------------------------------------//
    @PostMapping("/test/status")
    public Result doTest(@RequestBody UserInputDto dto) {
        log.info("【commonConfig】{}【serverUpgrading】{}【dynamicConfig】{}【adminInfo】{}",
            test, environment.getProperty("dynamic.serverUpgrading", Boolean.class),
            dynamicConfig.isServerUpgrading(),
            JSON.toJSONString(dynamicConfig.getAdminInfo())
        );

        boolean success = userServiceApi.addOrUpdateUser(dto);

        return Result.buildSuccess(success);
    }

    //----------------------------------------------------------------------------------------------------------------//
    @GetMapping("/test/jimmer/user")
    public List<@FetchBy("user_company_fetacher") User> testJimmer() throws JsonProcessingException {
        UserTable userTable = Tables.USER_TABLE;
        List<User> execute = userRepository.sql().createQuery(userTable)
            .where(userTable.company().id().eqIf(1))
            .select(userTable.fetch(user_company_fetacher)).execute();
        return execute;
    }

    @GetMapping("/test/jimmer/company")
    public Result testJimmerCompany() {
        CompanyTable table = Tables.COMPANY_TABLE;
        Integer affectedRowCount = companyRepository.sql()
            .createDelete(table)
            .where(table.id().eq(3))
            .execute();

        return Result.buildSuccess(affectedRowCount);
    }

    //----------------------------------------------------------------------------------------------------------------//
    private static final Fetcher<User> simple_user_fetcher =
        USER_FETCHER
            .allScalarFields()
            .userDetail(Fetchers.USER_DETAIL_FETCHER.signature())
            .roles(Fetchers.ROLE_FETCHER.allScalarFields());

    public static final Fetcher<User> user_company_fetacher =
        USER_FETCHER
            .allScalarFields()
            .company();
}
