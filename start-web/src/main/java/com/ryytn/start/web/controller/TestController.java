package com.ryytn.start.web.controller;

import com.alibaba.fastjson2.JSON;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.ryytn.start.api.dto.UserInputDto;
import com.ryytn.start.api.dto.McpTestRequestDto;
import com.ryytn.start.api.dto.McpTestResponseDto;
import com.ryytn.start.api.entity.CompanyTable;
import com.ryytn.start.api.entity.Fetchers;
import com.ryytn.start.api.entity.Tables;
import com.ryytn.start.api.entity.User;
import com.ryytn.start.api.entity.UserTable;
import com.ryytn.start.api.service.http.HttpUserServiceApi;
import com.ryytn.start.common.Result;
import com.ryytn.start.common.enums.BaseBizExceptionEnums;
import com.ryytn.start.manager.config.DynamicConfig;
import com.ryytn.start.manager.repository.CompanyRepository;
import com.ryytn.start.manager.repository.UserRepository;
import lombok.extern.slf4j.Slf4j;
import org.babyfish.jimmer.client.FetchBy;
import org.babyfish.jimmer.sql.fetcher.Fetcher;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

import static com.ryytn.start.api.entity.Fetchers.USER_FETCHER;

/**
 * <AUTHOR>
 * @since 2020-10-14
 */
@RestController
@Slf4j
@Validated
public class TestController {

    @Value("${common}")
    private String test;

    @Resource
    private Environment environment;
    @Resource
    private DynamicConfig dynamicConfig;
    @Resource
    private HttpUserServiceApi userServiceApi;

    @Autowired
    private UserRepository userRepository;
    @Autowired
    private CompanyRepository companyRepository;

    //----------------------------------------------------------------------------------------------------------------//
    @PostMapping("/test/status")
    public Result doTest(@RequestBody UserInputDto dto) {
        log.info("【commonConfig】{}【serverUpgrading】{}【dynamicConfig】{}【adminInfo】{}",
            test, environment.getProperty("dynamic.serverUpgrading", Boolean.class),
            dynamicConfig.isServerUpgrading(),
            JSON.toJSONString(dynamicConfig.getAdminInfo())
        );

        boolean success = userServiceApi.addOrUpdateUser(dto);

        return Result.buildSuccess(success);
    }

    //----------------------------------------------------------------------------------------------------------------//
    @GetMapping("/test/jimmer/user")
    public List<@FetchBy("user_company_fetacher") User> testJimmer() throws JsonProcessingException {
        UserTable userTable = Tables.USER_TABLE;
        List<User> execute = userRepository.sql().createQuery(userTable)
            .where(userTable.company().id().eqIf(1))
            .select(userTable.fetch(user_company_fetacher)).execute();
        return execute;
    }

    @GetMapping("/test/jimmer/company")
    public Result testJimmerCompany() {
        CompanyTable table = Tables.COMPANY_TABLE;
        Integer affectedRowCount = companyRepository.sql()
            .createDelete(table)
            .where(table.id().eq(3))
            .execute();

        return Result.buildSuccess(affectedRowCount);
    }

    //----------------------------------------------------------------------------------------------------------------//
    private static final Fetcher<User> simple_user_fetcher =
        USER_FETCHER
            .allScalarFields()
            .userDetail(Fetchers.USER_DETAIL_FETCHER.signature())
            .roles(Fetchers.ROLE_FETCHER.allScalarFields());

    public static final Fetcher<User> user_company_fetacher =
        USER_FETCHER
            .allScalarFields()
            .company();

    //----------------------------------------------------------------------------------------------------------------//
    // MCP 测试接口
    //----------------------------------------------------------------------------------------------------------------//

    /**
     * MCP测试POST接口
     * 接收复杂参数，执行业务逻辑，返回处理结果
     */
    @PostMapping("/mcp/test/post")
    public Result mcpTestPost(@RequestBody @Validated McpTestRequestDto request) {
        log.info("【MCP测试POST接口】接收到请求: {}", JSON.toJSONString(request));

        long startTime = System.currentTimeMillis();
        String processId = UUID.randomUUID().toString();

        try {
            // 模拟业务处理逻辑
            Thread.sleep(100); // 模拟处理时间

            // 构造响应数据
            String resultMessage = String.format("处理完成 - 用户: %s, 操作: %s, 优先级: %d",
                request.getUsername(), request.getOperation(), request.getPriority());

            com.alibaba.fastjson2.JSONObject resultData = new com.alibaba.fastjson2.JSONObject();
            resultData.put("username", request.getUsername());
            resultData.put("operation", request.getOperation());
            resultData.put("data", request.getData());
            resultData.put("priority", request.getPriority());
            resultData.put("enabled", request.getEnabled());
            resultData.put("processedAt", LocalDateTime.now().toString());

            long duration = System.currentTimeMillis() - startTime;
            McpTestResponseDto response = McpTestResponseDto.success(processId, resultMessage, resultData);
            response.setDuration(duration);

            log.info("【MCP测试POST接口】处理完成，耗时: {}ms", duration);
            return Result.buildSuccess(response);

        } catch (Exception e) {
            log.error("【MCP测试POST接口】处理异常", e);
            McpTestResponseDto response = McpTestResponseDto.failure(processId, "处理失败: " + e.getMessage());
            return Result.buildFail(BaseBizExceptionEnums.BUSINESS_ERROR);
        }
    }

    /**
     * MCP测试GET接口
     * 接收简单查询参数，返回查询结果
     */
    @GetMapping("/mcp/test/get")
    public Result mcpTestGet(
            @RequestParam(required = false, defaultValue = "guest") String username,
            @RequestParam(required = false, defaultValue = "query") String operation,
            @RequestParam(required = false, defaultValue = "1") Integer priority,
            @RequestParam(required = false, defaultValue = "true") Boolean enabled) {

        log.info("【MCP测试GET接口】接收到请求 - username: {}, operation: {}, priority: {}, enabled: {}",
            username, operation, priority, enabled);

        long startTime = System.currentTimeMillis();
        String processId = UUID.randomUUID().toString();

        try {
            // 模拟查询逻辑
            Thread.sleep(50); // 模拟查询时间

            String resultMessage = String.format("查询完成 - 用户: %s, 操作: %s, 优先级: %d, 状态: %s",
                username, operation, priority, enabled ? "启用" : "禁用");

            com.alibaba.fastjson2.JSONObject resultData = new com.alibaba.fastjson2.JSONObject();
            resultData.put("username", username);
            resultData.put("operation", operation);
            resultData.put("priority", priority);
            resultData.put("enabled", enabled);
            resultData.put("queryTime", LocalDateTime.now().toString());
            resultData.put("serverInfo", environment.getProperty("spring.application.name", "unknown"));

            long duration = System.currentTimeMillis() - startTime;
            McpTestResponseDto response = McpTestResponseDto.success(processId, resultMessage, resultData);
            response.setDuration(duration);

            log.info("【MCP测试GET接口】查询完成，耗时: {}ms", duration);
            return Result.buildSuccess(response);

        } catch (Exception e) {
            log.error("【MCP测试GET接口】查询异常", e);
            McpTestResponseDto response = McpTestResponseDto.failure(processId, "查询失败: " + e.getMessage());
            return Result.buildFail(BaseBizExceptionEnums.BUSINESS_ERROR);
        }
    }
}
