package com.ryytn.start.manager.repository;

import com.ryytn.start.api.entity.Tables;
import com.ryytn.start.api.entity.User;
import com.ryytn.start.api.entity.UserTable;
import org.babyfish.jimmer.spring.repository.JRepository;
import org.babyfish.jimmer.sql.fetcher.Fetcher;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>
 * UserRepository 接口
 * </p>
 *
 * <AUTHOR>
 * @date 2024-09-30
 */
@Repository
public interface UserRepository extends JRepository<User, Integer> {

    UserTable table = Tables.USER_TABLE;

    default List<User> findByPmsName(String permissionName, Fetcher<User> fetcher) {
        return sql().createQuery(table)
            .where(table.roles(roleTableEx -> roleTableEx.permissions(
                permissionTableEx -> permissionTableEx.name().eqIf(permissionName))))
            .select(table.fetch(fetcher))
            .execute();
    }

}

