### datasource
spring:
  datasource:
    url: **********************************************************************************************************************************************
    username: start
    password: Start123!@#
  redis:
    host: redis-tech-test.ryytngroup.com
    port: 6379
    database: 1
xxl:
  job:
    accessToken: ryytn
    admin:
      # 调度中心部署跟地址 [选填]：如调度中心集群部署存在多个地址则用逗号分隔。执行器将会使用该地址进行"执行器心跳注册"和"任务结果回调"；为空则关闭自动注册；
      addresses: https://xxl-job.ryytngroup.com/xxl-job-admin