package com.ryytn.start.service.test.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.hutool.log.Log;
import com.ryytn.start.service.test.common.Constant;
import com.ryytn.start.service.test.common.ContentEnum;
import com.ryytn.start.service.test.common.Message;
import com.ryytn.start.service.test.common.RoleEnum;
import com.ryytn.start.service.test.service.CowEyeService;
import com.ryytn.start.service.test.service.LongRunningService;
import com.ryytn.start.service.test.tongyi.TongyiAiUtils;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

import static com.ryytn.start.service.test.common.Constant.TongYiModelList;

/**
 * TestController
 *
 * <AUTHOR>
 * @date 11/6/24
 */
@Slf4j
@RestController
@RequestMapping("/test")
public class TestController {

    @Autowired
    private CowEyeService cowEyeService;

    @Autowired
    private TongyiAiUtils tongyiAiUtils;

    @Autowired
    private LongRunningService longRunningService;

    /**
     * 识别和牛牛肉等级
     */
    @SneakyThrows
    @PostMapping("/moonshot/coweye")
    public int moonshotCoweye(@RequestParam("file") MultipartFile multipartFile) {
        // todo
        if (multipartFile.isEmpty()) {
            return 0;
        }
        String suffix = FileUtil.getSuffix(multipartFile.getOriginalFilename());
        suffix = StrUtil.isBlank(suffix) ? suffix : "." + suffix;
        File tempFile = FileUtil.createTempFile("cow", suffix, true);
        multipartFile.transferTo(tempFile);
        int level = cowEyeService.identifyCowLevel(tempFile);
        return level;
    }

    @SneakyThrows
    @PostMapping("/tongyi/coweye")
    public JSONObject tongyiCoweye(@RequestParam("file") MultipartFile multipartFile,
        @RequestParam String model) {
        JSONObject result = new JSONObject().set("success", true)
            .set("error", null);

        if (multipartFile.isEmpty()) {
            return result.set("error", "upload file is empty!");
        }
        if (!TongYiModelList.contains(model)) {
            return result.set("error", "通义千问暂不支持该版本大模型！");
        }

        String suffix = FileUtil.getSuffix(multipartFile.getOriginalFilename());
        suffix = StrUtil.isBlank(suffix) ? suffix : "." + suffix;
        File tempFile = FileUtil.createTempFile("cow", suffix, true);
        multipartFile.transferTo(tempFile);
        String location = tongyiAiUtils.uploadFile(tempFile);
        Log.get().info("[image url] {}", location);

        Message message = getMessage(location);
        String chat = tongyiAiUtils.chat(model, CollUtil.newArrayList(message));
        String finalLevel = Constant.Bms2JapanLevelMap.get(chat.toUpperCase());
        if (StrUtil.isBlank(finalLevel)) {
            return result.set("error", "无法识别级别，请检查图片！");
        } else {
            return result.set("data", chat);
        }
    }

    private static Message getMessage(String imgUrl) {
        JSONObject jsonObject = new JSONObject().set("url", imgUrl);
        Message.TYContentObj tyContentObj = Message.TYContentObj.builder()
            .type(ContentEnum.text.name()).text(
                "请根据提供的牛肉图片，使用BMS（Beef Marbling Standard）标准来评估牛肉的质量。第一步：先分析图片是否是牛肉，如果不是则直接回答[该图片不是牛肉]；如果是牛肉，则进行第二步：分析图片中的牛肉纹理、脂肪分布和颜色等特征，并将其与BMS标准中的M1至M12等级进行比较。输出一个最终的等级结果，该结果必须是M1至M12中的一个等级。无需提供任何解释或分析过程，只需给出最终等级。")
            .build();
        Message.TYContentObj tyContentObj1 = Message.TYContentObj.builder()
            .type(ContentEnum.image_url.name()).image_url(jsonObject)
            .build();
        JSONArray jsonArray = new JSONArray();
        jsonArray.addAll(CollUtil.newArrayList(tyContentObj, tyContentObj1));
        Message message = Message.builder().role(RoleEnum.user.name())
            .content(JSONUtil.toJsonStr(jsonArray))
            .build();
        return message;
    }

    @GetMapping("/long-task")
    public String handleLongTask() {
        try {
            CompletableFuture<String> future = longRunningService.performLongTask();
            // 设置超时时间为10秒
            String string = future.orTimeout(10, TimeUnit.SECONDS).get();
            log.info("future get result finish or timeout...");
            return string;
        } catch (InterruptedException | ExecutionException e) {
            return "发生错误: " + e.getMessage();
        }
    }

}
