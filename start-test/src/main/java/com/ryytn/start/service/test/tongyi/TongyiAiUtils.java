package com.ryytn.start.service.test.tongyi;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.MD5;
import cn.hutool.http.ContentType;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.aliyun.oss.ClientBuilderConfiguration;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.common.auth.CredentialsProvider;
import com.aliyun.oss.common.auth.DefaultCredentialProvider;
import com.aliyun.oss.common.comm.SignVersion;
import com.aliyun.oss.model.GenericRequest;
import com.aliyun.oss.model.UploadFileRequest;
import com.aliyun.oss.model.UploadFileResult;
import com.ryytn.start.service.test.common.ContentEnum;
import com.ryytn.start.service.test.common.Message;
import com.ryytn.start.service.test.common.RoleEnum;
import lombok.NonNull;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Call;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.io.BufferedReader;
import java.io.File;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * TongyiAiUtils
 *
 * <AUTHOR>
 * @date 11/7/24
 */
@Component
@Slf4j
public class TongyiAiUtils {

    private static final String OSS_API_KEY = "LTAI5tMJo1i4YGrBvS3KHv97";
    private static final String OSS_API_SECRET = "******************************";
    private static final String OSS_URL = "https://oss-cn-hangzhou.aliyuncs.com";
    private static final String OSS_BUCKET = "ryytn-ai-img";
    private static final String OSS_DIR_PREFIX = "BeefCattle/";

    private static final int MAX_ATTEMPTS = 3; // 最大尝试次数
    private static final int BACKOFF_DELAY = 1; // 重试间隔，单位为秒
    private static final String CHAT_COMPLETION_URL = "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions";
    private static final String API_KEY = "sk-024a5618de2b431ab75bbaf8e8454062";

    private static OSS ossClient;

    @PostConstruct
    public void postConstruct() {
        CredentialsProvider credentialsProvider = new DefaultCredentialProvider(OSS_API_KEY, OSS_API_SECRET);

        ClientBuilderConfiguration clientBuilderConfiguration = new ClientBuilderConfiguration();
        clientBuilderConfiguration.setSignatureVersion(SignVersion.V4);
        OSS client = OSSClientBuilder.create()
            .endpoint(OSS_URL)
            .credentialsProvider(credentialsProvider)
            .clientConfiguration(clientBuilderConfiguration)
            .region("cn-hangzhou")
            .build();
        ossClient = client;
    }

    @PreDestroy
    public void preDestroy() {
        if (ObjUtil.isNotNull(ossClient)) {
            ossClient.shutdown();
        }
    }

    private String buildFileKey(File file) {
        if (ObjUtil.isNull(file)) {
            return null;
        }
        String suffix = FileUtil.getSuffix(file);
        String string = MD5.create().digestHex(file);
        return OSS_DIR_PREFIX + string + "." + suffix;
    }

    @SneakyThrows
    public String uploadFile(File file) {
        assert file != null;
        String fileKey = buildFileKey(file);
        // 先查oss是否有这张图片
        boolean objectExist = ossClient.doesObjectExist(new GenericRequest(OSS_BUCKET, fileKey));
        if (objectExist) {
            // 删除临时文件
            boolean del = FileUtil.del(file);
            log.info("[info] temp file delete result is: {}", del);

            // 直接返回对象地址
            return "https://" + OSS_BUCKET + ".oss-cn-hangzhou.aliyuncs.com/" + fileKey;
        }

        UploadFileRequest request = new UploadFileRequest(OSS_BUCKET, fileKey);
        request.setUploadFile(FileUtil.getAbsolutePath(file));
        UploadFileResult uploadFileResult = ossClient.uploadFile(request);
        String location = uploadFileResult.getMultipartUploadResult().getLocation();

        // 删除临时文件
        boolean del = FileUtil.del(file);
        log.info("[info] temp file delete result is: {}", del);

        return location;
    }

    public String chat(@NonNull String model, @NonNull List<Message> messages) {
        return chatWithRetry(model, messages, MAX_ATTEMPTS);
    }

    public String chatWithRetry(@NonNull String model, @NonNull List<Message> messages, int retryCount) {
        String requestBody = new JSONObject()
            .putOpt("model", model)
            .putOpt("messages", messages)
            .putOpt("stream", true)
            .toString();

        Request okhttpRequest = new Request.Builder()
            .url(CHAT_COMPLETION_URL)
            .post(RequestBody.create(requestBody, MediaType.get(ContentType.JSON.getValue())))
            .addHeader("Authorization", "Bearer " + API_KEY)
            .addHeader("Content-Type", "application/json")
            .build();

        StringBuilder resultBuilder = new StringBuilder();
        for (int i = 0; i < retryCount; i++) {
            // 清空内容，防止还保留上一次请求的内容
            resultBuilder.setLength(0);

            OkHttpClient okHttpClient = new OkHttpClient();
            Call call = okHttpClient.newCall(okhttpRequest);
            try (Response okhttpResponse = call.execute();
                BufferedReader reader = new BufferedReader(okhttpResponse.body().charStream())) {
                if (!okhttpResponse.isSuccessful()) {
                    throw new RuntimeException("moonshot chat api invoke error!");
                }

                String line;
                while ((line = reader.readLine()) != null) {
                    if (StrUtil.isBlank(line)) {
                        continue;
                    }
                    if (JSONUtil.isTypeJSON(line)) {
                        Optional.of(JSONUtil.parseObj(line))
                            .map(x -> x.getJSONObject("error"))
                            .map(x -> x.getStr("message"))
                            .ifPresent(resultBuilder::append);
                        break;
                    }
                    // data: {"id":"chatcmpl-672b149e733d5715668ee4bf","object":"chat.completion.chunk","created":1730876574,"model":"moonshot-v1-128k","choices":[{"index":0,"delta":{"content":"需要"},"finish_reason":null}],"system_fingerprint":"fpv0_6042e4b4"}
                    line = StrUtil.replace(line, "data: ", StrUtil.EMPTY);
                    if (StrUtil.equals("[DONE]", line) || !JSONUtil.isTypeJSON(line)) {
                        break;
                    }
                    Optional.of(JSONUtil.parseObj(line))
                        .map(x -> x.getJSONArray("choices"))
                        .filter(CollUtil::isNotEmpty)
                        .map(x -> (JSONObject) x.get(0))
                        .map(x -> x.getJSONObject("delta"))
                        .map(x -> x.getStr("content"))
                        .ifPresent(x -> {
                            if (StrUtil.isNotBlank(x)) {
                                resultBuilder.append(x);
                            }
                        });
                }
                break;
            } catch (Exception e) {
                log.error("moonshot chat api execute error!", e);
                ThreadUtil.sleep(BACKOFF_DELAY, TimeUnit.SECONDS);
            }
        }

        return resultBuilder.toString();
    }

    public static void main(String[] args) {
        String imgUrl = "https://ryytn-ai-img.oss-cn-hangzhou.aliyuncs.com/BeefCattle/3411787ed2c0521c30448f0de9b4268f.jpg";
        TongyiAiUtils tongyiAiUtils = new TongyiAiUtils();

        JSONObject jsonObject = new JSONObject().set("url", imgUrl);
        Message.TYContentObj tyContentObj = Message.TYContentObj.builder()
            .type(ContentEnum.text.name()).text(
                "请根据提供的牛肉图片，使用BMS（Beef Marbling Standard）标准来评估牛肉的质量。第一步：先分析图片是否是牛肉，如果不是则直接回答[该图片不是牛肉]；如果是牛肉，则进行第二步：分析图片中的牛肉纹理、脂肪分布和颜色等特征，并将其与BMS标准中的M1至M12等级进行比较。输出一个最终的等级结果，该结果必须是M1至M12中的一个等级。无需提供任何解释或分析过程，只需给出最终等级。")
            .build();
        Message.TYContentObj tyContentObj1 = Message.TYContentObj.builder()
            .type(ContentEnum.image_url.name()).image_url(jsonObject)
            .build();
        JSONArray jsonArray = new JSONArray();
        jsonArray.addAll(CollUtil.newArrayList(tyContentObj, tyContentObj1));
        Message message = Message.builder().role(RoleEnum.user.name())
            .content(JSONUtil.toJsonStr(jsonArray))
            .build();

        String chat = tongyiAiUtils.chat("qwen-vl-max-latest", CollUtil.newArrayList(message));
        System.out.println(chat);
    }

}
