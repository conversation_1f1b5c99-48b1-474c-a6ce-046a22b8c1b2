package com.ryytn.start.service.test.common;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class Message {

    private String role;

    private String content;

    /**
     * 通义内容对象
     */
    @Data
    @Builder
    public static class TYContentObj {
        private String type;
        private String text;
        private JSONObject image_url;
        private JSONArray video;
    }

}