package com.ryytn.start.service.test.moonshot;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.ContentType;
import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.Method;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.ryytn.start.service.test.common.Message;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Call;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

import java.io.BufferedReader;
import java.io.File;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

@Slf4j
public class MoonshotAiUtils {

    private static final String API_KEY = "sk-feXMFzzEXgoZrWWZVKWtRSUoXmaw2wgQGFAMXKutaIzth6mx";
    private static final String MODELS_URL = "https://api.moonshot.cn/v1/models";
    private static final String FILES_URL = "https://api.moonshot.cn/v1/files";
    private static final String ESTIMATE_TOKEN_COUNT_URL = "https://api.moonshot.cn/v1/tokenizers/estimate-token-count";
    private static final String CHAT_COMPLETION_URL = "https://api.moonshot.cn/v1/chat/completions";
    private static final int MAX_ATTEMPTS = 3; // 最大尝试次数
    private static final int BACKOFF_DELAY = 1; // 重试间隔，单位为秒

    public static String getModelList() {
        return getCommonRequest(MODELS_URL)
            .execute()
            .body();
    }

    public static String uploadFile(@NonNull File file) {
        return getCommonRequest(FILES_URL)
            .method(Method.POST)
            .header("purpose", "file-extract")
            .form("file", file)
            .execute()
            .body();
    }

    public static String getFileList() {
        return getCommonRequest(FILES_URL)
            .execute()
            .body();
    }

    public static String deleteFile(@NonNull String fileId) {
        return getCommonRequest(FILES_URL + "/" + fileId)
            .method(Method.DELETE)
            .execute()
            .body();
    }

    public static String getFileDetail(@NonNull String fileId) {
        return getCommonRequest(FILES_URL + "/" + fileId)
            .execute()
            .body();
    }

    public static String getFileContent(@NonNull String fileId) {
        return getCommonRequest(FILES_URL + "/" + fileId + "/content")
            .execute()
            .body();
    }

    public static String estimateTokenCount(@NonNull String model, @NonNull List<Message> messages) {
        String requestBody = new JSONObject()
            .putOpt("model", model)
            .putOpt("messages", messages)
            .toString();
        return getCommonRequest(ESTIMATE_TOKEN_COUNT_URL)
            .method(Method.POST)
            .header(Header.CONTENT_TYPE, ContentType.JSON.getValue())
            .body(requestBody)
            .execute()
            .body();
    }

    public static String chat(@NonNull String model, @NonNull List<Message> messages) {
        return chatWithRetry(model, messages, MAX_ATTEMPTS);
    }

    public static String chatWithRetry(@NonNull String model, @NonNull List<Message> messages, int retryCount) {
        String requestBody = new JSONObject()
            .putOpt("model", model)
            .putOpt("messages", messages)
            .putOpt("stream", true)
            .toString();

        Request okhttpRequest = new Request.Builder()
            .url(CHAT_COMPLETION_URL)
            .post(RequestBody.create(requestBody, MediaType.get(ContentType.JSON.getValue())))
            .addHeader("Authorization", "Bearer " + API_KEY)
            .build();

        StringBuilder resultBuilder = new StringBuilder();
        for (int i = 0; i < retryCount; i++) {
            // 清空内容，防止还保留上一次请求的内容
            resultBuilder.setLength(0);

            OkHttpClient okHttpClient = new OkHttpClient();
            Call call = okHttpClient.newCall(okhttpRequest);
            try(Response okhttpResponse = call.execute();
                BufferedReader reader = new BufferedReader(okhttpResponse.body().charStream())) {
                if (!okhttpResponse.isSuccessful()) {
                    throw new RuntimeException("moonshot chat api invoke error!");
                }

                String line;
                while ((line = reader.readLine()) != null) {
                    if (StrUtil.isBlank(line)) {
                        continue;
                    }
                    if (JSONUtil.isTypeJSON(line)) {
                        Optional.of(JSONUtil.parseObj(line))
                            .map(x -> x.getJSONObject("error"))
                            .map(x -> x.getStr("message"))
                            .ifPresent(resultBuilder::append);
                        break;
                    }
                    // data: {"id":"chatcmpl-672b149e733d5715668ee4bf","object":"chat.completion.chunk","created":1730876574,"model":"moonshot-v1-128k","choices":[{"index":0,"delta":{"content":"需要"},"finish_reason":null}],"system_fingerprint":"fpv0_6042e4b4"}
                    line = StrUtil.replace(line, "data: ", StrUtil.EMPTY);
                    if (StrUtil.equals("[DONE]", line) || !JSONUtil.isTypeJSON(line)) {
                        break;
                    }
                    Optional.of(JSONUtil.parseObj(line))
                        .map(x -> x.getJSONArray("choices"))
                        .filter(CollUtil::isNotEmpty)
                        .map(x -> (JSONObject) x.get(0))
                        .map(x -> x.getJSONObject("delta"))
                        .map(x -> x.getStr("content"))
                        .ifPresent(x -> {
                            if (StrUtil.isNotBlank(x)) {
                                resultBuilder.append(x);
                            }
                        });
                }
                break;
            } catch (Exception e) {
                log.error("moonshot chat api execute error!", e);
                ThreadUtil.sleep(BACKOFF_DELAY, TimeUnit.SECONDS);
            }
        }

        return resultBuilder.toString();
    }

    private static HttpRequest getCommonRequest(@NonNull String url) {
        return HttpRequest.of(url).header(Header.AUTHORIZATION, "Bearer " + API_KEY);
    }

}