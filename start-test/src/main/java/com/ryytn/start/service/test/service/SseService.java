package com.ryytn.start.service.test.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * SseService
 *
 * <AUTHOR>
 * @date 12/25/24
 */
@Slf4j
@Service
public class SseService {

    private static final Map<String, SseEmitter> SSE_CACHE = new ConcurrentHashMap<>();


    public SseEmitter getConn(String clientId) {
        final SseEmitter sseEmitter = SSE_CACHE.get(clientId);

        if (sseEmitter != null) {
            return sseEmitter;
        } else {
            // 设置连接超时时间，需要配合配置项 spring.mvc.async.request-timeout: 600000 一起使用  
            final SseEmitter emitter = new SseEmitter(600_000L);
            // 注册超时回调，超时后触发
            emitter.onTimeout(() -> {
                log.info("连接已超时，正准备关闭，clientId = {}", clientId);
                SSE_CACHE.remove(clientId);
            });
            // 注册完成回调，调用 emitter.complete() 触发
            emitter.onCompletion(() -> {
                log.info("连接已关闭，正准备释放，clientId = {}", clientId);
                SSE_CACHE.remove(clientId);
                log.info("连接已释放，clientId = {}", clientId);
            });
            // 注册异常回调，调用 emitter.completeWithError() 触发
            emitter.onError(throwable -> {
                log.error("连接已异常，正准备关闭，clientId = {}", clientId, throwable);
                SSE_CACHE.remove(clientId);
            });

            SSE_CACHE.put(clientId, emitter);

            return emitter;
        }
    }

    /**
     * 模拟类似于 chatGPT 的流式推送回答  
     *
     * @param clientId 客户端 id  
     * @throws IOException 异常  
     * @throws InterruptedException 异常  
     */
    public void send(String clientId) throws IOException, InterruptedException {
        final SseEmitter emitter = SSE_CACHE.get(clientId);
        // 推流内容到客户端
        String[] messages = {
            "寻寻觅觅",
            "冷冷清清",
            "凄凄惨惨戚戚",
            "乍暖还寒时候",
            "最难将息",
            "三杯两盏淡酒",
            "怎敌他",
            "晚来风急",
            "雁过也",
            "正伤心",
            "却是旧时相识",
            "满地黄花堆积",
            "憔悴损",
            "如今有谁堪摘",
            "守著窗儿",
            "独自怎生得黑",
            "梧桐更兼细雨",
            "到黄昏",
            "点点滴滴",
            "这次第",
            "怎一个愁字了得",
        };
        
        for (String message : messages) {
            SseEmitter.SseEventBuilder eventBuilder = SseEmitter.event()
                .data(message);
            emitter.send(eventBuilder);
            Thread.sleep(1000); // 添加一些延迟，模拟流式输出
        }
            
        // 结束推流（complete之后就不能再使用了）
        emitter.complete();
        SSE_CACHE.remove(clientId);
    }

    public void closeConn(String clientId) {
        final SseEmitter sseEmitter = SSE_CACHE.get(clientId);
        if (sseEmitter != null) {
            sseEmitter.complete();
            SSE_CACHE.remove(clientId);
        }
    }

}
