package com.ryytn.start.service.test.common;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Constant
 *
 * <AUTHOR>
 * @date 11/8/24
 */
public interface Constant {

    /**
     * 澳洲和牛等级和日本和牛等级映射
     */
    Map<String, String> Bms2JapanLevelMap = MapUtil.unmodifiable(new HashMap<>() {
        private static final long serialVersionUID = -3411584950266490458L;

        {
            put("M1", "A1");
            put("M2", "A2");
            put("M3", "A3");
            put("M4", "A3");
            put("M5", "A4");
            put("M6", "A4");
            put("M7", "A4");
            put("M8", "A5");
            put("M9", "A5");
            put("M10", "A5");
            put("M11", "A5");
            put("M12", "A5");
        }
    });

    /**
     * 通义模型列表
     */
    List<String> TongYiModelList = CollUtil.newArrayList("qwen-plus", "qwen-plus-latest", "qwen-turbo",
        "qwen-turbo-latest", "qwen-long", "qwen-vl-max", "qwen-vl-max-latest", "qwen-vl-plus", "qwen-vl-plus-latest",
        "qwen-audio-turbo", "qwen-math-plus", "qwen-math-plus-latest", "qwen-math-turbo", "qwen-math-turbo-latest",
        "qwen-coder-turbo", "qwen-coder-turbo-latest", "qwen2.5-72b-instruct", "qwen2.5-32b-instruct",
        "qwen2.5-14b-instruct", "qwen2.5-7b-instruct", "qwen2-vl-7b-instruct");

}
