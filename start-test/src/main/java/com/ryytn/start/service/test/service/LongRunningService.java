package com.ryytn.start.service.test.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.concurrent.CompletableFuture;

@Service
@Slf4j
public class LongRunningService {

    @Async
    public CompletableFuture<String> performLongTask() throws InterruptedException {
        log.info("start performLongTask...");
        // 模拟长时间的任务
        Thread.sleep(12000); // 睡眠12秒
        return CompletableFuture.completedFuture("任务完成");
    }
}