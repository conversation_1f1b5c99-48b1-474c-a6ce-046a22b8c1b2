package com.ryytn.start.service.test.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.crypto.digest.MD5;
import com.ryytn.start.service.test.common.Message;
import com.ryytn.start.service.test.common.RoleEnum;
import com.ryytn.start.service.test.moonshot.MoonshotAiUtils;
import lombok.extern.slf4j.Slf4j;
import org.noear.snack.ONode;
import org.springframework.stereotype.Service;

import java.io.File;

import static org.noear.snack.core.Feature.StringDoubleToDecimal;
import static org.noear.snack.core.Feature.StringJsonToNode;

/**
 * CowEyeService
 *
 * <AUTHOR>
 * @date 11/6/24
 */
@Service
@Slf4j
public class CowEyeService {

    private String lastFileMd5;
    private int lastLevel;

    /**
     * 通过AI识别和牛级别
     *
     * @param file 和牛横截面图片
     */
    public int identifyCowLevel(File file) {
        String fileMd5 = MD5.create().digestHex(file);
        if (ObjUtil.isNotEmpty(lastFileMd5) && fileMd5.contentEquals(lastFileMd5)) {
            return lastLevel;
        } else {
            lastFileMd5 = fileMd5;
        }

        // {"id":"cslei9h1jfd4cg6mn9i0","object":"file","bytes":75076,"created_at":1730865446,"filename":"cow16603045477363223859.png","purpose":"file-extract","status":"ok","status_details":""}
        // String string = MoonshotAiUtils.uploadFile(file);


        return 0;
    }

    public static void main(String[] args) {
        String fileObjStr = "{\"id\":\"cslei9h1jfd4cg6mn9i0\",\"object\":\"file\",\"bytes\":75076,\"created_at\":1730865446,\"filename\":\"cow16603045477363223859.png\",\"purpose\":\"file-extract\",\"status\":\"ok\",\"status_details\":\"\"}";
        ONode oNode = ONode.loadStr(fileObjStr, StringJsonToNode, StringDoubleToDecimal);
        String fileId = oNode.get("id").getString();
        // {"content":"清言·AI\n","file_type":"image/png","filename":"cow16603045477363223859.png","title":"","type":"file"}
        // String fileContent = MoonshotAiUtils.getFileContent(fileId);

        // {"id":"cslei9h1jfd4cg6mn9i0","object":"file","bytes":75076,"created_at":1730865446,"filename":"cow16603045477363223859.png","purpose":"file-extract","status":"ok","status_details":""}
        // String fileDetail = MoonshotAiUtils.getFileDetail(fileId);

        Message fileMessage = Message.builder().content("{\"id\":\"cslei9h1jfd4cg6mn9i0\",\"object\":\"file\",\"bytes\":75076,\"created_at\":1730865446,\"filename\":\"cow16603045477363223859.png\",\"purpose\":\"file-extract\",\"status\":\"ok\",\"status_details\":\"\"}")
            .role(RoleEnum.system.name()).build();
        Message userMessage = Message.builder()
            .content("okhttp3有什么方法可以将event-stream类型的流式响应结果汇聚成一个完整的字符串")
            .role(RoleEnum.user.name()).build();
        // rowData:
        // rowData: M
        // rowData: 5
        String chat = MoonshotAiUtils.chat("moonshot-v1-128k", CollUtil.newArrayList(userMessage));
        System.out.println(chat);

        // {"deleted":true,"id":"cslei9h1jfd4cg6mn9i0","object":"file"}
        // String delResult = MoonshotAiUtils.deleteFile(fileId);


    }

}
