package com.ryytn.start.service.test.controller;

import com.alibaba.fastjson2.JSONObject;
import com.ryytn.start.service.test.service.SseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.concurrent.CompletableFuture;

/**
 * Chat<PERSON>ontroller
 *
 * <AUTHOR>
 * @date 12/25/24
 */
@RestController
@Slf4j
public class ChatController {

    @Autowired
    private SseService service;

    @GetMapping(value = "/test/sse/{clientId}", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter test(@PathVariable("clientId") String clientId) {
        final SseEmitter emitter = service.getConn(clientId);
        
        // 设置响应头
        try {
            emitter.send(SseEmitter.event()
                .data("")
                .id("init")
                .comment("初始化连接")
                .build());
        } catch (IOException e) {
            log.error("初始化连接失败", e);
            emitter.completeWithError(e);
            return emitter;
        }
            
        CompletableFuture.runAsync(() -> {
            try {
                service.send(clientId);
            } catch (Exception e) {
                log.error("推送数据异常", e);
                throw new RuntimeException("推送数据异常", e);
            }
        });

        return emitter;
    }

    @GetMapping("closeConn/{clientId}")
    public JSONObject closeConn(@PathVariable("clientId") String clientId) {
        service.closeConn(clientId);
        return new JSONObject().fluentPut("msg", "连接已关闭");
    }

}
