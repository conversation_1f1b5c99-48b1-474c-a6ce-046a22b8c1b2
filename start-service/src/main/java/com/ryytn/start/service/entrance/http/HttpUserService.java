package com.ryytn.start.service.entrance.http;

import cn.hutool.extra.spring.SpringUtil;
import com.ryytn.start.api.dto.UserInputDto;
import com.ryytn.start.api.service.http.HttpUserServiceApi;
import com.ryytn.start.service.action.http.AddOrUpdateUser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * HttpUserService
 *
 * <AUTHOR>
 * @date 10/12/24
 */
@Slf4j
@RestController
public class HttpUserService implements HttpUserServiceApi {

    @Override
    @PostMapping("/addOrUpdateUser")
    public boolean addOrUpdateUser(@RequestBody UserInputDto userInputDto) {
        return SpringUtil.getBean(AddOrUpdateUser.class).invoke(userInputDto);
    }
}
