package com.ryytn.start.service.action.http;

import com.ryytn.start.api.dto.UserInputDto;
import com.ryytn.start.api.entity.User;
import com.ryytn.start.manager.repository.UserRepository;
import com.ryytn.start.service.action.AbstractAction;
import lombok.extern.slf4j.Slf4j;
import org.babyfish.jimmer.sql.ast.mutation.SaveMode;
import org.babyfish.jimmer.sql.ast.mutation.SimpleSaveResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * UpdateUser
 *
 * <AUTHOR>
 * @date 4/7/23
 */
@Slf4j
@Component
public class AddOrUpdateUser extends AbstractAction<UserInputDto, Boolean> {

    @Autowired
    private UserRepository userRepository;

    @Override
    protected Boolean doInvoke(UserInputDto request) {
        log.info("AddOrUpdateUser success!!!");
        SimpleSaveResult<User> saveResult = userRepository.sql()
            .save(request, SaveMode.UPDATE_ONLY);

        return saveResult.getAffectedRowCount(User.class) == 1;
    }

    @Override
    protected String actionName() {
        return "更新用户信息";
    }
}
