package com.ryytn.start.common;

import com.ryytn.start.common.enums.ErrorBaseEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>基础result</p>
 * <p>分页result可以使用{@link cn.hutool.db.PageResult}</p>
 *
 * <AUTHOR>
 * @since 2022/10/14
 */
@Data
public class Result implements Serializable {

  private static final long serialVersionUID = 7483367432829560094L;

  /**
   * 是否成功
   */
  private Boolean success;
  /**
   * 实际结果
   */
  private Object data;
  /**
   * 结果总数
   */
  private Long total;
  /**
   * 异常分类编码
   */
  private Integer code;
  /**
   * 异常信息
   */
  private String message;
  /**
   * 实际业务异常码 {@link ErrorBaseEnum#getKey()}
   */
  private String subCode;

  public Result() {
  }

  public static Result buildSuccess(Object data) {
    Result result = new Result();
    result.setCode(200);
    result.setSuccess(Boolean.TRUE);
    result.setData(data);
    return result;
  }

  public static Result buildSuccess(Object data, Long total) {
      Result result = buildSuccess(data);
    result.setTotal(total);
    return result;
  }

  public static Result buildSuccess() {
    Result result = new Result();
    result.setCode(200);
    result.setSuccess(Boolean.TRUE);
    result.setData(null);
    return result;
  }

  public static Result buildFail(ErrorBaseEnum error) {
    Result result = new Result();
    result.setSuccess(Boolean.FALSE);
    result.setCode(error.getCode());
    result.setSubCode(error.getKey());
    result.setData(null);
    return result;
  }
}
