<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>start</artifactId>
        <groupId>com.ryytn.start</groupId>
        <version>1.0.0-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>start-api</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.dtflys.forest</groupId>
            <artifactId>forest-core</artifactId>
        </dependency>

        <dependency>
            <groupId>org.babyfish.jimmer</groupId>
            <artifactId>jimmer-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.babyfish.jimmer</groupId>
            <artifactId>jimmer-sql</artifactId>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
	    <dependency>
		    <groupId>javax.validation</groupId>
		    <artifactId>validation-api</artifactId>
	    </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.babyfish.jimmer</groupId>
                            <artifactId>jimmer-apt</artifactId>
                            <version>${jimmer.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                    </annotationProcessorPaths>
                    <compilerArgs>
                        <arg>-Ajimmer.dto.defaultNullableInputModifier=dynamic</arg>
                    </compilerArgs>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>