package com.ryytn.start.api.dto;

import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * MCP测试请求DTO
 * <AUTHOR>
 * @since 2025-01-21
 */
@Data
public class McpTestRequestDto {

    /**
     * 用户名
     */
    @NotBlank(message = "用户名不能为空")
    private String username;

    /**
     * 操作类型
     */
    @NotBlank(message = "操作类型不能为空")
    private String operation;

    /**
     * 参数数据
     */
    private String data;

    /**
     * 优先级
     */
    @NotNull(message = "优先级不能为空")
    private Integer priority;

    /**
     * 是否启用
     */
    private Boolean enabled = true;
}
