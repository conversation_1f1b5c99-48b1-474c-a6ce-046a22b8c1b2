package com.ryytn.start.api.entity;

import org.babyfish.jimmer.sql.Column;
import org.babyfish.jimmer.sql.Default;
import org.babyfish.jimmer.sql.Entity;
import org.babyfish.jimmer.sql.GeneratedValue;
import org.babyfish.jimmer.sql.GenerationType;
import org.babyfish.jimmer.sql.Id;
import org.babyfish.jimmer.sql.LogicalDeleted;
import org.babyfish.jimmer.sql.OneToMany;
import org.babyfish.jimmer.sql.OneToOne;
import org.babyfish.jimmer.sql.OrderedProp;
import org.babyfish.jimmer.sql.Table;
import org.jetbrains.annotations.Nullable;

import java.util.List;


/**
 * <p>
 * company
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2024-09-30
 */
@Entity
@Table(name = "company")
public interface Company {

    /**
     * id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    int id();

    /**
     * name
     */
    @Nullable
    String name();

    /**
     * parent_id
     */
    @Column(name = "parent_id")
    @Nullable
    Integer parentId();

    /**
     * deleted
     */
    @Default("0")
    @LogicalDeleted("1")
    int deleted();

    @OneToOne(mappedBy = "company") //一对一镜像端，必须可空
    @Nullable
    CompanyDetail companyDetail();

    @OneToMany(mappedBy = "company", orderedProps = {
        @OrderedProp(value = "id", desc = true)
    }) //一对多必须不可空
    List<User> users();

}
