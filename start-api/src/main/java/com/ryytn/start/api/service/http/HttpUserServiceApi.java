package com.ryytn.start.api.service.http;

import com.dtflys.forest.annotation.Address;
import com.dtflys.forest.annotation.JSONBody;
import com.dtflys.forest.annotation.Post;
import com.ryytn.start.api.dto.UserInputDto;

/**
 * StarterUserService
 *
 * <AUTHOR>
 * @date 10/12/24
 */
@Address(basePath = "{start-web-url}")
public interface HttpUserServiceApi {

    @Post("/addOrUpdateUser")
    boolean addOrUpdateUser(@JSONBody UserInputDto userInputDto);

}
