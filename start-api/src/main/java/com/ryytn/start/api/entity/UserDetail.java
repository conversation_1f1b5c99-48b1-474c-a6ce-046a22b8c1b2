package com.ryytn.start.api.entity;

import org.babyfish.jimmer.sql.Entity;
import org.babyfish.jimmer.sql.ForeignKeyType;
import org.babyfish.jimmer.sql.GeneratedValue;
import org.babyfish.jimmer.sql.GenerationType;
import org.babyfish.jimmer.sql.Id;
import org.babyfish.jimmer.sql.IdView;
import org.babyfish.jimmer.sql.JoinColumn;
import org.babyfish.jimmer.sql.OneToOne;
import org.babyfish.jimmer.sql.Table;
import org.jetbrains.annotations.Nullable;


/**
 * <p>
 * user_detail
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2024-09-30
 */
@Entity
@Table(name = "user_detail")
public interface UserDetail {

    /**
     * id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    int id();

    /**
     * signature
     */
    @Nullable
    String signature();

    @Nullable //伪外键必须可空
    @IdView
    Integer userId();

    @Nullable //与(伪)外键是否可空保持一致
    @OneToOne
    @JoinColumn(name = "user_id", foreignKeyType = ForeignKeyType.FAKE)
    User user();

}
