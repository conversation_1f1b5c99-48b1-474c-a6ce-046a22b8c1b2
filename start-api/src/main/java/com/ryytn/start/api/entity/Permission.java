package com.ryytn.start.api.entity;

import org.babyfish.jimmer.sql.Entity;
import org.babyfish.jimmer.sql.GeneratedValue;
import org.babyfish.jimmer.sql.GenerationType;
import org.babyfish.jimmer.sql.Id;
import org.babyfish.jimmer.sql.JoinTable;
import org.babyfish.jimmer.sql.ManyToOne;
import org.babyfish.jimmer.sql.Table;
import org.jetbrains.annotations.Nullable;


/**
 * <p>
 * permission
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2024-09-30
 */
@Entity
@Table(name = "permission")
public interface Permission {

    /**
     * id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    int id();

    /**
     * name
     */
    @Nullable
    String name();

    @Nullable
    @ManyToOne //1.多对一基于中间表，必须可空
    // 2.对于不可空的多对一，不能用属性过滤器
    // 3.假如使用字段级过滤器，缓存会失效，如果不想，可以使用多视角缓存的全局过滤器
    @JoinTable(
        name = "role_permission",
        joinColumnName = "permission_id",
        inverseJoinColumnName = "role_id"
    )
    Role role();

}
