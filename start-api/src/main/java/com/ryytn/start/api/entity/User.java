package com.ryytn.start.api.entity;

import org.babyfish.jimmer.sql.Column;
import org.babyfish.jimmer.sql.DissociateAction;
import org.babyfish.jimmer.sql.Entity;
import org.babyfish.jimmer.sql.ForeignKeyType;
import org.babyfish.jimmer.sql.GeneratedValue;
import org.babyfish.jimmer.sql.GenerationType;
import org.babyfish.jimmer.sql.Id;
import org.babyfish.jimmer.sql.IdView;
import org.babyfish.jimmer.sql.JoinColumn;
import org.babyfish.jimmer.sql.JoinTable;
import org.babyfish.jimmer.sql.ManyToMany;
import org.babyfish.jimmer.sql.ManyToOne;
import org.babyfish.jimmer.sql.OnDissociate;
import org.babyfish.jimmer.sql.OneToOne;
import org.babyfish.jimmer.sql.Table;
import org.jetbrains.annotations.Nullable;

import java.time.LocalDateTime;
import java.util.List;


/**
 * <p>
 * user
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2024-09-30
 */
@Entity
@Table(name = "user")
public interface User {

    /**
     * id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    int id();

    /**
     * name
     */
    @Nullable
    String name();

    /**
     * balance
     */
    @Nullable
    Integer balance();

    /**
     * version
     */
    @Nullable
    Integer version();

    /**
     * create_time
     */
    @Column(name = "create_time")
    @Nullable
    LocalDateTime createTime();

    /**
     * update_time
     */
    @Column(name = "update_time")
    @Nullable
    LocalDateTime updateTime();

    /**
     * enabled
     */
    @Nullable
    Integer enabled();

    /**
     * deleted
     */
    @Nullable
    Integer deleted();

    /**
     * company_id
     */
    @IdView("company") //根据数据库中是否可为空，为空用Integer，不为空用int，关联的ManyToOne对象也保持一致
    Integer companyId();

    @OnDissociate(DissociateAction.SET_NULL)
    @Nullable // 与companyId保持一致
    @ManyToOne // inputNotNull = true 必须用于可空的对象，且对应数据库外键字段必须不能为空。
    @JoinColumn(name = "company_id", foreignKeyType = ForeignKeyType.REAL)
    Company company();

    @OneToOne(mappedBy = "user")
    @Nullable //一对一镜像端必须可空
    UserDetail userDetail();

    @ManyToMany //多对多必须不为空
    @JoinTable(
        name = "user_role",
        joinColumnName = "user_id",
        inverseJoinColumnName = "role_id"
    )
    List<Role> roles();

}
