package com.ryytn.start.api.dto;

import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * MCP测试响应DTO
 * <AUTHOR>
 * @since 2025-01-21
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class McpTestResponseDto {

    /**
     * 处理ID
     */
    private String processId;

    /**
     * 处理状态
     */
    private String status;

    /**
     * 处理消息
     */
    private String message;

    /**
     * 处理结果数据
     */
    private Object resultData;

    /**
     * 处理时间
     */
    private LocalDateTime processTime;

    /**
     * 耗时（毫秒）
     */
    private Long duration;

    /**
     * 创建成功响应
     */
    public static McpTestResponseDto success(String processId, String message, Object data) {
        return McpTestResponseDto.builder()
                .processId(processId)
                .status("SUCCESS")
                .message(message)
                .resultData(data)
                .processTime(LocalDateTime.now())
                .build();
    }

    /**
     * 创建失败响应
     */
    public static McpTestResponseDto failure(String processId, String message) {
        return McpTestResponseDto.builder()
                .processId(processId)
                .status("FAILURE")
                .message(message)
                .processTime(LocalDateTime.now())
                .build();
    }
}
