package com.ryytn.start.api.entity;

import org.babyfish.jimmer.sql.Entity;
import org.babyfish.jimmer.sql.GeneratedValue;
import org.babyfish.jimmer.sql.GenerationType;
import org.babyfish.jimmer.sql.Id;
import org.babyfish.jimmer.sql.ManyToMany;
import org.babyfish.jimmer.sql.OneToMany;
import org.babyfish.jimmer.sql.OrderedProp;
import org.babyfish.jimmer.sql.Table;
import org.jetbrains.annotations.Nullable;

import java.util.List;


/**
 * <p>
 * role
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2024-09-30
 */
@Entity
@Table(name = "role")
public interface Role {

    /**
     * id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    int id();

    /**
     * name
     */
    @Nullable
    String name();

    @OneToMany(mappedBy = "role", orderedProps = {
        @OrderedProp(value = "id", desc = true)
    }) //一对多必须不可空
    List<Permission> permissions();

    @ManyToMany(mappedBy = "roles", orderedProps = {
        @OrderedProp(value = "id", desc = true)
    }) //多对多必须不可空
    List<User> users();

}
