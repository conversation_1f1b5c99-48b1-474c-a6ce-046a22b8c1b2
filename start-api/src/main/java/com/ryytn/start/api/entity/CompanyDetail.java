package com.ryytn.start.api.entity;

import org.babyfish.jimmer.sql.DissociateAction;
import org.babyfish.jimmer.sql.Entity;
import org.babyfish.jimmer.sql.ForeignKeyType;
import org.babyfish.jimmer.sql.GeneratedValue;
import org.babyfish.jimmer.sql.GenerationType;
import org.babyfish.jimmer.sql.Id;
import org.babyfish.jimmer.sql.IdView;
import org.babyfish.jimmer.sql.JoinColumn;
import org.babyfish.jimmer.sql.OnDissociate;
import org.babyfish.jimmer.sql.OneToOne;
import org.babyfish.jimmer.sql.Table;
import org.jetbrains.annotations.Nullable;


/**
 * <p>
 * company_detail
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2024-09-30
 */
@Entity
@Table(name = "company_detail")
public interface CompanyDetail {

    /**
     * id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    int id();

    /**
     * address
     */
    @Nullable
    String address();

    @Nullable
    @IdView("company") //与数据库外键字段是否可空保持一致，如果是伪外键，则必须可空
    Integer companyId();

    @OnDissociate(DissociateAction.DELETE)
    @OneToOne
    @JoinColumn(name = "company_id", foreignKeyType = ForeignKeyType.FAKE)
    @Nullable //与数据库外键字段是否可空保持一致，如果是伪外键，则必须可空
    Company company();

}
