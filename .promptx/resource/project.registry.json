{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-07-21T06:06:09.172Z", "updatedAt": "2025-07-21T06:06:09.175Z", "resourceCount": 7}, "resources": [{"id": "mcp-tool-management", "source": "project", "protocol": "execution", "name": "Mcp Tool Management 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/mcp-manager/execution/mcp-tool-management.execution.md", "metadata": {"createdAt": "2025-07-21T06:06:09.173Z", "updatedAt": "2025-07-21T06:06:09.173Z", "scannedAt": "2025-07-21T06:06:09.173Z", "path": "role/mcp-manager/execution/mcp-tool-management.execution.md"}}, {"id": "mcp-manager", "source": "project", "protocol": "role", "name": "Mcp Manager 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/mcp-manager/mcp-manager.role.md", "metadata": {"createdAt": "2025-07-21T06:06:09.173Z", "updatedAt": "2025-07-21T06:06:09.173Z", "scannedAt": "2025-07-21T06:06:09.173Z", "path": "role/mcp-manager/mcp-manager.role.md"}}, {"id": "mcp-management-thinking", "source": "project", "protocol": "thought", "name": "Mcp Management Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/mcp-manager/thought/mcp-management-thinking.thought.md", "metadata": {"createdAt": "2025-07-21T06:06:09.174Z", "updatedAt": "2025-07-21T06:06:09.174Z", "scannedAt": "2025-07-21T06:06:09.174Z", "path": "role/mcp-manager/thought/mcp-management-thinking.thought.md"}}, {"id": "riper6-workflow", "source": "project", "protocol": "execution", "name": "Riper6 Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/riper6-dev/execution/riper6-workflow.execution.md", "metadata": {"createdAt": "2025-07-21T06:06:09.174Z", "updatedAt": "2025-07-21T06:06:09.174Z", "scannedAt": "2025-07-21T06:06:09.174Z", "path": "role/riper6-dev/execution/riper6-workflow.execution.md"}}, {"id": "riper6-dev", "source": "project", "protocol": "role", "name": "Riper6 Dev 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/riper6-dev/riper6-dev.role.md", "metadata": {"createdAt": "2025-07-21T06:06:09.174Z", "updatedAt": "2025-07-21T06:06:09.174Z", "scannedAt": "2025-07-21T06:06:09.174Z", "path": "role/riper6-dev/riper6-dev.role.md"}}, {"id": "riper6-thinking", "source": "project", "protocol": "thought", "name": "Riper6 Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/riper6-dev/thought/riper6-thinking.thought.md", "metadata": {"createdAt": "2025-07-21T06:06:09.175Z", "updatedAt": "2025-07-21T06:06:09.175Z", "scannedAt": "2025-07-21T06:06:09.174Z", "path": "role/riper6-dev/thought/riper6-thinking.thought.md"}}, {"id": "example-tool", "source": "project", "protocol": "tool", "name": "Example Tool tool", "description": "tool类型的资源", "reference": "@project://.promptx/resource/tool/example-tool/example-tool.tool.js", "metadata": {"createdAt": "2025-07-21T06:06:09.175Z", "updatedAt": "2025-07-21T06:06:09.175Z", "scannedAt": "2025-07-21T06:06:09.175Z", "path": "tool/example-tool/example-tool.tool.js"}}], "stats": {"totalResources": 7, "byProtocol": {"execution": 2, "role": 2, "thought": 2, "tool": 1}, "bySource": {"project": 7}}}